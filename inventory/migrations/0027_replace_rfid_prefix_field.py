# Generated manually

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('inventory', '0026_make_rfid_prefix_required'),
    ]

    operations = [
        # Remove the old CharField
        migrations.RemoveField(
            model_name='organization',
            name='rfid_prefix',
        ),
        
        # Add the new PositiveSmallIntegerField
        migrations.AddField(
            model_name='organization',
            name='rfid_prefix',
            field=models.PositiveSmallIntegerField(
                default=0,  # Temporary default to allow migration
                help_text="Numeric prefix for RFID EPCs (0-4095)"
            ),
            preserve_default=False,  # Remove the default after migration
        ),
        
        # Add unique constraint
        migrations.AlterField(
            model_name='organization',
            name='rfid_prefix',
            field=models.PositiveSmallIntegerField(
                unique=True,
                help_text="Numeric prefix for RFID EPCs (0-4095)"
            ),
        ),
    ]