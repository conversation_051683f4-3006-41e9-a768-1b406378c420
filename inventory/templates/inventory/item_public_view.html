{% extends 'base_minimal.html' %}
{% load inventory_filters %}

{% block title %}{{ item.item_name }} - Public View{% endblock %}

{% block content %}
<div class="container">
    {% if item.organization %}
        <div class="text-center mb-4">
            {% if item.organization.logo %}
                <img src="{{ item.organization.logo.url }}" alt="{{ item.organization.name }}" style="max-height: 80px;" class="mb-3">
            {% endif %}
            <h4>{{ item.organization.name }} Inventory</h4>
        </div>
        
        <style>
            :root {
                --primary: {{ item.organization.primary_color }};
                --secondary: {{ item.organization.secondary_color }};
                --bs-primary: {{ item.organization.primary_color }};
                --bs-primary-rgb: {{ item.organization.primary_color|hex_to_rgb }};
                --bs-secondary: {{ item.organization.secondary_color }};
                --bs-secondary-rgb: {{ item.organization.secondary_color|hex_to_rgb }};
            }
            
            .card {
                border-left: 3px solid var(--primary);
            }
            
            .code-box { 
                padding: 10px; 
                border: 1px solid #ddd; 
                display: inline-block;
                font-family: monospace;
                font-size: 1.2em;
                margin-bottom: 10px;
                background-color: #f8f9fa;
            }
            
            .status-badge {
                display: inline-block;
                padding: 0.25rem 0.5rem;
                font-size: 0.875rem;
                font-weight: 500;
                border-radius: 0.25rem;
                background-color: var(--secondary);
                color: white;
                margin-left: 0.5rem;
            }
        </style>
    {% endif %}

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ item.item_name }}</h5>
            {% if item.status %}
            <span class="status-badge">{{ item.status.value }}</span>
            {% endif %}
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6>Item Code</h6>
                    <div class="code-box">{{ item.item_code }}</div>
                </div>
                <div class="col-md-6">
                    <h6>EPC (Electronic Product Code)</h6>
                    {% if item.epc %}
                        <div class="code-box" style="font-size: 0.9em;">{{ item.epc_formatted }}</div>
                    {% else %}
                        <p class="text-muted">EPC not available</p>
                    {% endif %}
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-12">
                    <h6>Location</h6>
                    <p>
                        <span class="text-muted">Location tracking is not available</span>
                    </p>
                </div>
            </div>
            
            {% if item.item_description %}
            <div class="mb-3">
                <h6>Description</h6>
                <p>{{ item.item_description }}</p>
            </div>
            {% endif %}
            
            {% if item.custom_fields %}
            <h6>Custom Fields</h6>
            <table class="table table-sm">
                <tbody>
                    {% for key, value in item.custom_fields.items %}
                    <tr>
                        <th scope="row" style="width: 30%;">{{ key }}</th>
                        <td>{{ value }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
