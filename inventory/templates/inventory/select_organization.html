{% extends 'base_minimal.html' %}
{% load inventory_filters %}

{% block title %}Select Organization - Inventory Management{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Select Organization</h4>
                    {% if user.is_superuser %}
                    <a href="/admin/" class="btn btn-light btn-sm">
                        <i class="bi bi-gear-fill"></i> Admin Interface
                    </a>
                    {% endif %}
                </div>
                <div class="card-body">
                    <p class="lead mb-4">Please select an organization to continue:</p>
                    
                    {% if organizations %}
                    <div class="row">
                        {% for org in organizations %}
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 {% if org.id|stringformat:'i' == request.session.org_id %}border-primary{% endif %}">
                                    <div class="card-body d-flex flex-column">
                                        {% if org.logo %}
                                            <div class="text-center mb-3">
                                                <img src="{{ org.logo.url }}" alt="{{ org.name }}" class="img-fluid" style="max-height: 80px;">
                                            </div>
                                        {% endif %}
                                        <h5 class="card-title">{{ org.name }}</h5>
                                        <p class="card-text text-muted">Code: {{ org.code }}</p>
                                        <div class="mt-auto">
                                            <form method="post" action="{% url 'inventory:switch_organization' org_id=org.id %}">
                                                {% csrf_token %}
                                                <input type="hidden" name="next" value="{{ next_url }}">
                                                <button type="submit" class="btn btn-primary w-100">
                                                    {% if org.id|stringformat:'i' == request.session.org_id %}
                                                        Currently Selected
                                                    {% else %}
                                                        Select
                                                    {% endif %}
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">No Organizations Available</h5>
                        <p>There are no organizations available for your account.</p>
                        {% if user.is_superuser %}
                        <hr>
                        <p class="mb-0">As an administrator, you can create organizations in the admin interface.</p>
                        <a href="/admin/inventory/organization/add/" class="btn btn-primary mt-3">
                            Create Organization
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <div class="mt-4 text-center">
                        {% if user.is_superuser %}
                        <a href="/admin/inventory/organization/add/" class="btn btn-success">
                            <i class="bi bi-plus-circle"></i> Create New Organization
                        </a>
                        {% endif %}
                        <a href="{% url 'logout' %}" class="btn btn-outline-secondary ms-2">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
