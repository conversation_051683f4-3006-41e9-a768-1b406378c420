{% extends 'base.html' %}
{% load inventory_filters %}

{% block title %}Items - Inventory Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Inventory Items</h1>
    <div>
        <a href="{% url 'inventory:add_item' %}" class="btn btn-primary">Add New Item</a>
    </div>
</div>

<!-- Remove the debug information alert box -->

<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">Search and Filter</h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                {{ form.search.label_tag }}
                {{ form.search }}
            </div>
            <div class="col-md-2">
                {{ form.item_type.label_tag }}
                {{ form.item_type }}
            </div>
            <div class="col-md-2">
                {{ form.status.label_tag }}
                {{ form.status }}
            </div>
            <div class="col-md-2">
                {{ form.location.label_tag }}
                {{ form.location }}
            </div>
            <div class="col-md-6">
                <div class="form-check mt-2">
                    {{ form.show_archived }}
                    <label class="form-check-label" for="{{ form.show_archived.id_for_label }}">
                        Show Archived Items
                    </label>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <button type="submit" class="btn btn-primary">Apply Filters</button>
                <a href="{% url 'inventory:item_list' %}" class="btn btn-outline-secondary">Reset</a>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Items</h5>
        <!-- Remove the bulk move button -->
    </div>
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <!-- Remove the checkbox column -->
                    <th scope="col">Code</th>
                    <th scope="col">Image</th>
                    <th scope="col">Name</th>
                    <th scope="col">Status</th>
                    <th scope="col">Location</th>
                    <th scope="col">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for item in page_obj %}
                <tr>
                    <!-- Remove the checkbox cell -->
                    <td><code>{{ item.item_code }}</code></td>
                    <td>
                        {% if item.image %}
                        <a href="{% url 'inventory:item_by_code' item_code=item.item_code %}">
                            <img src="{{ item.image.url }}" alt="{{ item.item_name }}"
                                 style="max-height: 40px; max-width: 40px;" class="img-thumbnail">
                        </a>
                        {% endif %}
                    </td>
                    <td>
                        <a href="{% url 'inventory:item_by_code' item_code=item.item_code %}">{{ item.item_name }}</a>
                        {% if item.is_archived %}<span class="badge bg-secondary">Archived</span>{% endif %}
                    </td>
                    <td>{{ item.status.value }}</td>
                    <td>
                        {{ item|location_breadcrumb }}
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="{% url 'inventory:item_by_code' item_code=item.item_code %}" class="btn btn-outline-primary">
                                View
                            </a>
                            <a href="{% url 'inventory:edit_item_by_code' item_code=item.item_code %}" class="btn btn-outline-secondary">
                                Edit
                            </a>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="text-center py-4">No items found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

{% if page_obj.has_other_pages %}
<nav class="mt-4">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">First</a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Previous</a>
        </li>
        {% else %}
        <li class="page-item disabled">
            <span class="page-link">First</span>
        </li>
        <li class="page-item disabled">
            <span class="page-link">Previous</span>
        </li>
        {% endif %}

        {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                </li>
            {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Next</a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Last</a>
        </li>
        {% else %}
        <li class="page-item disabled">
            <span class="page-link">Next</span>
        </li>
        <li class="page-item disabled">
            <span class="page-link">Last</span>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const selectAll = document.getElementById('select-all');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox');
        const bulkMoveBtn = document.getElementById('bulk-move-btn');

        // Handle select all checkbox
        selectAll.addEventListener('change', function() {
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            updateBulkButtonState();
        });

        // Handle individual checkboxes
        itemCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateBulkButtonState();

                // Update select all checkbox state
                const allChecked = Array.from(itemCheckboxes).every(c => c.checked);
                const noneChecked = Array.from(itemCheckboxes).every(c => !c.checked);

                selectAll.checked = allChecked;
                selectAll.indeterminate = !allChecked && !noneChecked;
            });
        });

        // Update bulk action button state
        function updateBulkButtonState() {
            const anyChecked = Array.from(itemCheckboxes).some(c => c.checked);
            bulkMoveBtn.disabled = !anyChecked;
        }
    });
</script>
{% endblock %}
