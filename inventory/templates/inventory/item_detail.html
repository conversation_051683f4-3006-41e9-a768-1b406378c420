{% extends 'base.html' %}
{% load inventory_filters %}

{% block title %}{{ item.item_name }} - Inventory Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ item.item_name }}</h1>
    <div class="btn-group" role="group">
        <a href="{% url 'inventory:edit_item_by_code' item_code=item.item_code %}" class="btn btn-primary">Edit Item</a>
        {% if not item.is_archived %}
        <a href="{% url 'inventory:archive_item_by_code' item_code=item.item_code %}" class="btn btn-outline-danger">Archive Item</a>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-9">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Item Details</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>Item Code</h6>
                        <div class="code-box">{{ item.item_code }}</div>
                    </div>
                    <div class="col-md-6">
                        <h6>Location</h6>
                        <p>
                            {% if item.located_in %}
                                <a href="{% url 'inventory:item_detail' pk=item.located_in.pk %}">
                                    {{ item.located_in.item_name }} ({{ item.located_in.item_code }})
                                </a>
                                <a href="{% url 'inventory:move_item' pk=item.pk %}" class="btn btn-sm btn-outline-secondary ms-2">
                                    <i class="fas fa-exchange-alt"></i> Move
                                </a>
                            {% else %}
                                <span class="text-muted">Top Level (Not in any container)</span>
                                <a href="{% url 'inventory:move_item' pk=item.pk %}" class="btn btn-sm btn-outline-secondary ms-2">
                                    <i class="fas fa-exchange-alt"></i> Move
                                </a>
                            {% endif %}
                        </p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <h6>Item Type</h6>
                        <p>{{ item.item_type.value }}</p>
                    </div>
                    <div class="col-md-4">
                        <h6>Status</h6>
                        <p>{{ item.status.value }}</p>
                    </div>
                    <div class="col-md-4">
                        <h6>Last Updated</h6>
                        <p>{{ item.last_updated|date:"F j, Y, P" }}</p>
                    </div>
                </div>

                <h6>Description</h6>
                <p>{{ item.item_description|default:"No description provided." }}</p>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>Item Code</h6>
                        <p><code>{{ item.item_code }}</code></p>
                    </div>

                </div>

                {% if item.custom_fields %}
                <h6>Custom Fields</h6>
                <table class="table table-sm">
                    <tbody>
                        {% for key, value in item.custom_fields.items %}
                        <tr>
                            <th scope="row" style="width: 30%;">{{ key }}</th>
                            <td>{{ value }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% endif %}

                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>Date Added</h6>
                        <p>{{ item.date_added|date:"F j, Y, P" }}</p>
                    </div>
                </div>

                {% if item.is_archived %}
                <div class="alert alert-warning mt-3">
                    <strong>Archived Item</strong> - This item has been archived.
                </div>
                {% endif %}
            </div>
        </div>

        {% if contained_items %}
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Contained Items ({{ contained_items.count }})</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for contained_item in contained_items %}
                            <tr>
                                <td><code>{{ contained_item.item_code }}</code></td>
                                <td>{{ contained_item.item_name }}</td>
                                <td>{{ contained_item.item_type.value }}</td>
                                <td>{{ contained_item.status.value }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'inventory:item_detail' pk=contained_item.pk %}" class="btn btn-outline-primary">View</a>
                                        <a href="{% url 'inventory:move_item' pk=contained_item.pk %}" class="btn btn-outline-secondary">Move</a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-md-3">
        {% if item.image %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Image</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <a href="{{ item.image.url }}" target="_blank">
                        <img src="{{ item.image.url }}" alt="{{ item.image_caption|default:item.item_name }}"
                             class="img-fluid rounded">
                    </a>
                    {% if item.image_caption %}
                    <p class="text-center mt-1"><small>{{ item.image_caption }}</small></p>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        

    </div>
</div>
{% endblock %}
