from django import forms
from django.core.exceptions import ValidationError
from django.forms.widgets import Select, TextInput
from django.utils.html import format_html
from .models import Item, ManagedListValue, Organization, ItemImage


class ItemCodeLookupWidget(TextInput):
    """Custom widget for item code lookup with autocomplete"""

    template_name = 'inventory/widgets/item_code_lookup.html'

    def __init__(self, attrs=None):
        default_attrs = {
            'class': 'form-control item-code-lookup',
            'placeholder': 'Enter item code or name',
            'autocomplete': 'off',
        }
        if attrs:
            default_attrs.update(attrs)
        super().__init__(default_attrs)

    def get_context(self, name, value, attrs):
        context = super().get_context(name, value, attrs)

        # Add a hidden input for the actual value
        context['widget']['hidden_input_attrs'] = {
            'type': 'hidden',
            'name': name,
            'id': f"{attrs.get('id')}_hidden",
            'value': value or '',
        }

        # Add a display field for the selected item
        context['widget']['display_attrs'] = {
            'id': f"{attrs.get('id')}_display",
            'class': 'form-control-plaintext item-display',
            'readonly': True,
            'style': 'display: none;',
        }

        # Add a clear button
        context['widget']['clear_attrs'] = {
            'id': f"{attrs.get('id')}_clear",
            'class': 'btn btn-sm btn-outline-secondary clear-item-btn',
            'type': 'button',
            'style': 'display: none;',
        }

        return context

    class Media:
        css = {
            'all': ('css/item_code_lookup.css',)
        }
        js = ('js/item_code_lookup.js',)


class AttributeSelect(Select):
    """Custom Select widget that supports option attributes"""

    def __init__(self, attrs=None, choices=(), option_attrs=None):
        self.option_attrs = option_attrs or {}
        super().__init__(attrs, choices)

    def create_option(self, name, value, label, selected, index, subindex=None, attrs=None):
        option = super().create_option(name, value, label, selected, index, subindex, attrs)

        # Add any option-specific attributes
        if value in self.option_attrs:
            for key, val in self.option_attrs[value].items():
                option['attrs'][key] = val

        return option


class ItemSearchForm(forms.Form):
    """Form for searching and filtering items"""
    search = forms.CharField(required=False, widget=forms.TextInput(attrs={
        'placeholder': 'Search by name or code',
        'class': 'form-control'
    }))
    organization = forms.ModelChoiceField(
        queryset=Organization.objects.filter(is_active=True),
        required=False,
        empty_label="All Organizations",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    item_type = forms.ModelChoiceField(
        queryset=ManagedListValue.objects.filter(list_name='ItemTypes', is_active=True),
        required=False,
        empty_label="All Types",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    status = forms.ModelChoiceField(
        queryset=ManagedListValue.objects.filter(list_name='ItemStatuses', is_active=True),
        required=False,
        empty_label="All Statuses",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    location = forms.ModelChoiceField(
        queryset=None,  # Will be populated in __init__
        required=False,
        empty_label="All Locations",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    show_archived = forms.BooleanField(required=False, initial=False)

    def __init__(self, *args, **kwargs):
        organization_id = kwargs.pop('organization_id', None)
        super().__init__(*args, **kwargs)

        # Set initial organization if provided
        if organization_id:
            self.fields['organization'].initial = organization_id

            # Filter item types and statuses by organization
            self.fields['item_type'].queryset = ManagedListValue.objects.filter(
                list_name='ItemTypes',
                is_active=True,
                organization_id=organization_id
            )

            self.fields['status'].queryset = ManagedListValue.objects.filter(
                list_name='ItemStatuses',
                is_active=True,
                organization_id=organization_id
            )

        # Get all items as potential containers
        container_query = Item.objects.filter(is_archived=False)

        # Filter by organization if provided
        if organization_id:
            container_query = container_query.filter(organization_id=organization_id)

        container_items = container_query.order_by('item_name')

        self.fields['location'].queryset = container_items


class ItemForm(forms.ModelForm):
    """Form for adding/editing items"""

    
    class Meta:
        model = Item
        fields = ['item_name', 'item_description', 'item_type', 'status', 'located_in', 'image', 'image_caption']
        widgets = {
            'item_name': forms.TextInput(attrs={'class': 'form-control'}),
            'item_description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'item_type': forms.Select(attrs={'class': 'form-select'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'located_in': ItemCodeLookupWidget(),
            'image': forms.ClearableFileInput(attrs={'class': 'form-control'}),
            'image_caption': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Optional image description'}),
        }

    def __init__(self, *args, **kwargs):
        self.organization_id = kwargs.pop('organization_id', None)
        super().__init__(*args, **kwargs)

        # Filter location choices by organization
        if self.organization_id:
            self.fields['located_in'].queryset = Item.objects.filter(
                organization_id=self.organization_id,
                is_archived=False
            ).order_by('item_name')

    def _get_all_contained_items(self, item):
        """Recursively get all items contained within an item"""
        result = []
        direct_children = Item.objects.filter(located_in=item)

        for child in direct_children:
            result.append(child)
            result.extend(self._get_all_contained_items(child))

        return result

    def clean(self):
        cleaned_data = super().clean()
        located_in = cleaned_data.get('located_in')

        # If this is an existing item being edited
        instance = getattr(self, 'instance', None)
        if instance and instance.pk and self.organization_id:
            # Ensure organization isn't being changed
            if str(instance.organization_id) != str(self.organization_id):
                raise forms.ValidationError(
                    "You cannot change an item's organization."
                )

            # Validate location hierarchy for existing items
            if located_in:
                try:
                    instance.validate_location_hierarchy(located_in)
                except ValidationError as e:
                    self.add_error('located_in', str(e))
        elif located_in:
            # For new items, we need to create a temporary instance to validate
            # We can't use the actual instance because it doesn't have a pk yet
            from .models import Item
            temp_item = Item(pk=0)  # Temporary ID for validation
            try:
                temp_item.validate_location_hierarchy(located_in)
            except ValidationError as e:
                self.add_error('located_in', str(e))

        return cleaned_data


class LabelGenerationForm(forms.Form):
    """Form for generating a batch of labels"""
    quantity = forms.IntegerField(
        min_value=1,
        max_value=1000,
        initial=100,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )


class ItemImageForm(forms.ModelForm):
    """Form for uploading images to an item"""
    class Meta:
        model = ItemImage
        fields = ['image', 'caption', 'is_primary']
        widgets = {
            'caption': forms.TextInput(attrs={'class': 'form-control'}),
            'is_primary': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class ItemImageFormSet(forms.inlineformset_factory(
    Item,
    ItemImage,
    form=ItemImageForm,
    extra=1,
    can_delete=True,
    can_order=True,  # This enables the ORDER field
    fields=['image', 'caption', 'is_primary', 'order'],  # Include 'order' field
)):
    """Formset for managing multiple images for an item"""
    pass


class MoveItemForm(forms.Form):
    """Form for moving an item to a new location/container"""
    target_location = forms.ModelChoiceField(
        queryset=Item.objects.filter(is_archived=False),
        required=False,
        empty_label="Top Level (No Container)",
        widget=ItemCodeLookupWidget()
    )

    def __init__(self, *args, **kwargs):
        self.item = kwargs.pop('item', None)
        self.organization_id = kwargs.pop('organization_id', None)
        super().__init__(*args, **kwargs)

        # Add a hidden field for the current item ID
        if self.item and self.item.pk:
            # This will be used by the JavaScript to exclude the current item and its children
            self.fields['current_item_id'] = forms.CharField(
                widget=forms.HiddenInput(),
                initial=self.item.pk,
                required=False
            )

    def _get_all_contained_items(self, item):
        """Recursively get all items contained within an item"""
        result = []
        direct_children = Item.objects.filter(located_in=item)

        for child in direct_children:
            result.append(child)
            result.extend(self._get_all_contained_items(child))

        return result

    def clean_target_location(self):
        target_location = self.cleaned_data.get('target_location')

        # Validate that we're not creating a circular reference
        if self.item and target_location:
            try:
                self.item.validate_location_hierarchy(target_location)
            except ValidationError as e:
                raise forms.ValidationError(str(e))

        return target_location


def get_container_items(organization_id=None, exclude_ids=None):
    """Helper function to get all potential container items"""
    # All items can be containers now
    container_query = Item.objects.filter(is_archived=False)

    # Filter by organization if provided
    if organization_id:
        container_query = container_query.filter(organization_id=organization_id)

    # Exclude specific items if needed
    if exclude_ids:
        container_query = container_query.exclude(pk__in=exclude_ids)

    return container_query.order_by('item_name')
