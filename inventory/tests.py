from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from .models import Organization, Item, ManagedListValue, OrganizationUser, ItemImage
from .templatetags.inventory_filters import location_breadcrumb
import secrets


class OrganizationModelTest(TestCase):
    """Test cases for the Organization model"""

    def setUp(self):
        """Set up test data"""
        self.valid_org_data = {
            'name': 'Test Organization',
            'code': 'TEST001',
            'is_active': True,
            'primary_color': '#007bff',
            'secondary_color': '#6c757d'
        }

    def test_create_organization_with_valid_data(self):
        """Test creating an organization with valid data"""
        org = Organization.objects.create(**self.valid_org_data)
        self.assertEqual(org.name, 'Test Organization')
        self.assertEqual(org.code, 'TEST001')

        self.assertTrue(org.is_active)
        self.assertEqual(org.primary_color, '#007bff')
        self.assertEqual(org.secondary_color, '#6c757d')

    def test_organization_str_representation(self):
        """Test the string representation of Organization"""
        org = Organization.objects.create(**self.valid_org_data)
        self.assertEqual(str(org), 'Test Organization')





    def test_unique_constraints(self):
        """Test unique constraints on code"""
        # Create first organization
        org1 = Organization.objects.create(**self.valid_org_data)

        # Test duplicate code
        org_data_dup_code = self.valid_org_data.copy()
        with self.assertRaises(IntegrityError):
            Organization.objects.create(**org_data_dup_code)

    def test_default_values(self):
        """Test default values for optional fields"""
        minimal_data = {
            'name': 'Minimal Org',
            'code': 'MIN001'
        }
        org = Organization.objects.create(**minimal_data)

        self.assertTrue(org.is_active)  # Default should be True
        self.assertEqual(org.primary_color, '#007bff')  # Default value
        self.assertEqual(org.secondary_color, '#6c757d')  # Default value
        self.assertFalse(org.logo)  # Should be falsy (empty ImageFieldFile)

    def test_logo_upload(self):
        """Test logo image upload"""
        # Create a simple test image file
        image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
        uploaded_file = SimpleUploadedFile("test_logo.png", image_content, content_type="image/png")

        org_data = self.valid_org_data.copy()
        org_data['logo'] = uploaded_file

        org = Organization.objects.create(**org_data)
        self.assertTrue(org.logo)
        self.assertTrue(org.logo.name.startswith('org_logos/'))

    def test_color_field_validation(self):
        """Test color field format validation"""
        # Valid color formats should work
        valid_colors = ['#000000', '#ffffff', '#123abc', '#ABC123']

        for i, color in enumerate(valid_colors):
            org_data = self.valid_org_data.copy()
            org_data['code'] = f'COLOR{i:03d}'

            org_data['primary_color'] = color

            org = Organization.objects.create(**org_data)
            self.assertEqual(org.primary_color, color)


class ItemModelTest(TestCase):
    """Test cases for the Item model"""

    def setUp(self):
        """Set up test data"""
        # Create test organization
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001',
            is_active=True
        )

        # Create test managed list values
        self.item_type = ManagedListValue.objects.create(
            organization=self.organization,
            list_name='ItemTypes',
            value='Electronics',
            is_active=True
        )

        self.item_status = ManagedListValue.objects.create(
            organization=self.organization,
            list_name='ItemStatuses',
            value='Available',
            is_active=True
        )

        self.valid_item_data = {
            'item_name': 'Test Item',
            'item_description': 'A test item for testing',
            'organization': self.organization,
            'item_type': self.item_type,
            'status': self.item_status,
            'is_archived': False
        }

    def test_create_item_with_valid_data(self):
        """Test creating an item with valid data"""
        item = Item.objects.create(**self.valid_item_data)

        self.assertEqual(item.item_name, 'Test Item')
        self.assertEqual(item.item_description, 'A test item for testing')
        self.assertEqual(item.organization, self.organization)
        self.assertEqual(item.item_type, self.item_type)
        self.assertEqual(item.status, self.item_status)
        self.assertFalse(item.is_archived)

        # Check that item_code was automatically generated
        self.assertIsNotNone(item.item_code)
        self.assertEqual(len(item.item_code), 6)  # 3 bytes = 6 hex chars



    def test_item_str_representation(self):
        """Test the string representation of Item"""
        item = Item.objects.create(**self.valid_item_data)
        expected_str = f"Test Item ({item.item_code})"
        self.assertEqual(str(item), expected_str)

    def test_item_code_generation_uniqueness(self):
        """Test that item codes are unique"""
        items = []
        for i in range(10):
            item_data = self.valid_item_data.copy()
            item_data['item_name'] = f'Test Item {i}'
            items.append(Item.objects.create(**item_data))

        # Check all item codes are unique
        item_codes = [item.item_code for item in items]
        self.assertEqual(len(item_codes), len(set(item_codes)))

    def test_item_code_property_getter_setter(self):
        """Test item_code property getter and setter"""
        item = Item.objects.create(**self.valid_item_data)

        # Test getter returns hex string
        original_code = item.item_code
        self.assertIsInstance(original_code, str)
        self.assertEqual(len(original_code), 6)

        # Test setter with hex string
        new_code = 'abcdef'
        item.item_code = new_code
        self.assertEqual(item.item_code, new_code)

        # Test setter with bytes
        item.item_code = bytes.fromhex('123456')
        self.assertEqual(item.item_code, '123456')



    def test_location_hierarchy_validation_self_reference(self):
        """Test validation prevents self-reference in location"""
        item = Item.objects.create(**self.valid_item_data)

        # Try to set item as its own location
        with self.assertRaises(ValidationError) as context:
            item.validate_location_hierarchy(item)

        self.assertIn("cannot be located inside itself", str(context.exception))

    def test_location_hierarchy_validation_circular_reference(self):
        """Test validation prevents circular references"""
        # Create three items
        item1 = Item.objects.create(item_name='Item 1', organization=self.organization)
        item2 = Item.objects.create(item_name='Item 2', organization=self.organization)
        item3 = Item.objects.create(item_name='Item 3', organization=self.organization)

        # Set up hierarchy: item1 -> item2 -> item3
        item2.located_in = item1
        item2.save()
        item3.located_in = item2
        item3.save()

        # Try to create circular reference: item1 -> item3 (which would create cycle)
        with self.assertRaises(ValidationError) as context:
            item1.validate_location_hierarchy(item3)

        self.assertIn("circular reference", str(context.exception))

    def test_location_hierarchy_valid_cases(self):
        """Test valid location hierarchy cases"""
        container = Item.objects.create(item_name='Container', organization=self.organization)
        item = Item.objects.create(item_name='Item', organization=self.organization)

        # Valid: item inside container
        self.assertTrue(item.validate_location_hierarchy(container))

        # Valid: no location (None)
        self.assertTrue(item.validate_location_hierarchy(None))

    def test_contained_item_count(self):
        """Test contained_item_count property"""
        container = Item.objects.create(item_name='Container', organization=self.organization)

        # Initially no items
        self.assertEqual(container.contained_item_count, 0)

        # Add some items
        for i in range(3):
            Item.objects.create(
                item_name=f'Item {i}',
                organization=self.organization,
                located_in=container
            )

        self.assertEqual(container.contained_item_count, 3)

        # Archived items shouldn't count
        archived_item = Item.objects.create(
            item_name='Archived Item',
            organization=self.organization,
            located_in=container,
            is_archived=True
        )

        self.assertEqual(container.contained_item_count, 3)  # Still 3, not 4

    def test_get_absolute_url(self):
        """Test get_absolute_url method"""
        item = Item.objects.create(**self.valid_item_data)
        expected_url = f'/inventory/item/{item.item_code}/'
        # Note: This assumes the URL pattern exists - adjust if different
        self.assertEqual(item.get_absolute_url(), expected_url)

    def test_image_properties(self):
        """Test image-related properties"""
        item = Item.objects.create(**self.valid_item_data)

        # Initially no image
        self.assertFalse(item.has_images)
        self.assertIsNone(item.primary_image)
        self.assertIsNone(item.image_url)

        # Add image
        image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
        uploaded_file = SimpleUploadedFile("test_item.png", image_content, content_type="image/png")

        item.image = uploaded_file
        item.save()

        self.assertTrue(item.has_images)
        self.assertIsNotNone(item.primary_image)
        self.assertIsNotNone(item.image_url)

    def test_epc_generation_with_valid_data(self):
        """Test EPC generation with valid organization and item code"""
        # Set a specific rfid_prefix for predictable testing
        self.organization.rfid_prefix = 123
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)

        # Manually set item code for predictable testing
        item.item_code = 'abcdef'  # 3 bytes: 0xabcdef

        epc_int = item.generate_epc()
        self.assertIsNotNone(epc_int)

        # Verify bit structure: [Org Prefix: 12 bits][Padding: 60 bits][Item Code: 24 bits]
        # Expected: 0x7b000000000000000000abcdef (123 = 0x7b)
        expected_org_prefix = 123
        expected_item_code = 0xabcdef
        expected_padding = 0

        # Extract components from generated EPC
        extracted_item_code = epc_int & 0xFFFFFF  # Last 24 bits
        extracted_padding = (epc_int >> 24) & 0xFFFFFFFFFFFFFFF  # Next 60 bits
        extracted_org_prefix = (epc_int >> 84) & 0xFFF  # First 12 bits

        self.assertEqual(extracted_org_prefix, expected_org_prefix)
        self.assertEqual(extracted_padding, expected_padding)
        self.assertEqual(extracted_item_code, expected_item_code)

    def test_epc_property_hex_format(self):
        """Test EPC property returns properly formatted hex string"""
        self.organization.rfid_prefix = 255  # 0xFF
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)
        item.item_code = '123456'

        epc_hex = item.epc
        self.assertIsNotNone(epc_hex)
        self.assertEqual(len(epc_hex), 24)  # 96 bits = 24 hex chars
        self.assertTrue(all(c in '0123456789ABCDEF' for c in epc_hex))

        # Verify it starts with the organization prefix (255 = 0xFF = 0x0FF in 12 bits)
        self.assertTrue(epc_hex.startswith('0FF'))

    def test_epc_formatted_property(self):
        """Test EPC formatted property adds spaces for readability"""
        self.organization.rfid_prefix = 1
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)

        epc_formatted = item.epc_formatted
        self.assertIsNotNone(epc_formatted)

        # Should have spaces every 4 characters
        parts = epc_formatted.split(' ')
        self.assertEqual(len(parts), 6)  # 24 chars / 4 = 6 parts
        for part in parts:
            self.assertEqual(len(part), 4)

    def test_epc_generation_without_organization(self):
        """Test EPC generation fails gracefully without organization"""
        item_data = self.valid_item_data.copy()
        item_data['organization'] = None

        # This should fail due to model constraints, but let's test the EPC method directly
        item = Item(**item_data)
        item._item_code = bytes.fromhex('123456')

        epc_int = item.generate_epc()
        self.assertIsNone(epc_int)

        epc_hex = item.epc
        self.assertIsNone(epc_hex)

        epc_formatted = item.epc_formatted
        self.assertIsNone(epc_formatted)

    def test_epc_generation_without_rfid_prefix(self):
        """Test EPC generation fails gracefully without rfid_prefix"""
        # Create organization without rfid_prefix
        org_without_prefix = Organization(
            name='No Prefix Org',
            code='NOPREFIX',
            rfid_prefix=None
        )

        item_data = self.valid_item_data.copy()
        item_data['organization'] = org_without_prefix

        item = Item(**item_data)
        item._item_code = bytes.fromhex('123456')

        epc_int = item.generate_epc()
        self.assertIsNone(epc_int)

    def test_epc_generation_without_item_code(self):
        """Test EPC generation fails gracefully without item code"""
        self.organization.rfid_prefix = 100
        self.organization.save()

        item_data = self.valid_item_data.copy()
        item = Item(**item_data)
        item._item_code = None

        epc_int = item.generate_epc()
        self.assertIsNone(epc_int)

    def test_epc_bit_boundaries(self):
        """Test EPC generation with boundary values"""
        # Test maximum values for each component
        self.organization.rfid_prefix = 4095  # Maximum 12-bit value
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)
        item.item_code = 'ffffff'  # Maximum 24-bit value

        epc_int = item.generate_epc()
        self.assertIsNotNone(epc_int)

        # Verify components are properly masked
        extracted_item_code = epc_int & 0xFFFFFF
        extracted_org_prefix = (epc_int >> 84) & 0xFFF

        self.assertEqual(extracted_org_prefix, 4095)
        self.assertEqual(extracted_item_code, 0xFFFFFF)

    def test_epc_generation_with_oversized_values(self):
        """Test EPC generation properly masks oversized values"""
        # Set rfid_prefix larger than 12 bits (should be masked)
        self.organization.rfid_prefix = 8191  # 13 bits, should be masked to 12 bits
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)
        # Item code is already limited to 3 bytes by the model

        epc_int = item.generate_epc()
        self.assertIsNotNone(epc_int)

        # Verify organization prefix is properly masked to 12 bits
        extracted_org_prefix = (epc_int >> 84) & 0xFFF
        expected_masked_prefix = 8191 & 0xFFF  # Should be 4095 (0xFFF)
        self.assertEqual(extracted_org_prefix, expected_masked_prefix)

    def test_epc_uniqueness_across_organizations(self):
        """Test that EPCs are unique across different organizations"""
        # Create second organization
        org2 = Organization.objects.create(
            name='Test Org 2',
            code='TEST002',
            rfid_prefix=456
        )

        self.organization.rfid_prefix = 123
        self.organization.save()

        # Create items with same item code in different organizations
        item1 = Item.objects.create(
            item_name='Item 1',
            organization=self.organization
        )
        item2 = Item.objects.create(
            item_name='Item 2',
            organization=org2
        )

        # Set same item code for both
        item1.item_code = 'abcdef'
        item2.item_code = 'abcdef'

        epc1 = item1.epc
        epc2 = item2.epc

        self.assertIsNotNone(epc1)
        self.assertIsNotNone(epc2)
        self.assertNotEqual(epc1, epc2)  # Should be different due to different org prefixes


class ManagedListValueModelTest(TestCase):
    """Test cases for the ManagedListValue model"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001'
        )

        self.valid_list_value_data = {
            'organization': self.organization,
            'list_name': 'ItemTypes',
            'value': 'Electronics',
            'is_active': True
        }

    def test_create_managed_list_value(self):
        """Test creating a managed list value"""
        mlv = ManagedListValue.objects.create(**self.valid_list_value_data)

        self.assertEqual(mlv.organization, self.organization)
        self.assertEqual(mlv.list_name, 'ItemTypes')
        self.assertEqual(mlv.value, 'Electronics')
        self.assertTrue(mlv.is_active)

    def test_managed_list_value_str_representation(self):
        """Test string representation"""
        mlv = ManagedListValue.objects.create(**self.valid_list_value_data)
        expected_str = "Electronics (ItemTypes)"
        self.assertEqual(str(mlv), expected_str)

    def test_list_name_choices(self):
        """Test valid list name choices"""
        valid_choices = ['ItemTypes', 'ItemStatuses']

        for choice in valid_choices:
            data = self.valid_list_value_data.copy()
            data['list_name'] = choice
            data['value'] = f'Test {choice}'

            mlv = ManagedListValue.objects.create(**data)
            self.assertEqual(mlv.list_name, choice)

    def test_unique_together_constraint(self):
        """Test unique_together constraint"""
        # Create first value
        ManagedListValue.objects.create(**self.valid_list_value_data)

        # Try to create duplicate
        with self.assertRaises(IntegrityError):
            ManagedListValue.objects.create(**self.valid_list_value_data)

        # But different organization should work
        org2 = Organization.objects.create(
            name='Test Org 2',
            code='UNIQUE003'
        )

        data = self.valid_list_value_data.copy()
        data['organization'] = org2

        mlv2 = ManagedListValue.objects.create(**data)
        self.assertEqual(mlv2.organization, org2)

    def test_relationship_with_items(self):
        """Test relationship with Item model"""
        # Create list values
        item_type = ManagedListValue.objects.create(**self.valid_list_value_data)

        status_data = self.valid_list_value_data.copy()
        status_data['list_name'] = 'ItemStatuses'
        status_data['value'] = 'Available'
        item_status = ManagedListValue.objects.create(**status_data)

        # Create item using these values
        item = Item.objects.create(
            item_name='Test Item',
            organization=self.organization,
            item_type=item_type,
            status=item_status
        )

        # Test reverse relationships
        self.assertIn(item, item_type.items_of_type.all())
        self.assertIn(item, item_status.items_with_status.all())

    def test_default_values(self):
        """Test default values"""
        minimal_data = {
            'organization': self.organization,
            'list_name': 'ItemTypes',
            'value': 'Test Type'
        }

        mlv = ManagedListValue.objects.create(**minimal_data)
        self.assertTrue(mlv.is_active)  # Should default to True


class OrganizationUserModelTest(TestCase):
    """Test cases for the OrganizationUser model"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001'
        )

        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.valid_org_user_data = {
            'user': self.user,
            'organization': self.organization,
            'is_admin': False,
            'can_edit': True,
            'can_add': True
        }

    def test_create_organization_user(self):
        """Test creating an organization user"""
        org_user = OrganizationUser.objects.create(**self.valid_org_user_data)

        self.assertEqual(org_user.user, self.user)
        self.assertEqual(org_user.organization, self.organization)
        self.assertFalse(org_user.is_admin)
        self.assertTrue(org_user.can_edit)
        self.assertTrue(org_user.can_add)

    def test_organization_user_str_representation(self):
        """Test string representation"""
        org_user = OrganizationUser.objects.create(**self.valid_org_user_data)
        expected_str = f"{self.user.username} in {self.organization.name}"
        self.assertEqual(str(org_user), expected_str)

    def test_unique_together_constraint(self):
        """Test unique_together constraint for user and organization"""
        # Create first relationship
        OrganizationUser.objects.create(**self.valid_org_user_data)

        # Try to create duplicate
        with self.assertRaises(IntegrityError):
            OrganizationUser.objects.create(**self.valid_org_user_data)

        # But same user in different organization should work
        org2 = Organization.objects.create(
            name='Test Org 2',
            code='ORGUSER003'
        )

        data = self.valid_org_user_data.copy()
        data['organization'] = org2

        org_user2 = OrganizationUser.objects.create(**data)
        self.assertEqual(org_user2.organization, org2)

    def test_default_permission_values(self):
        """Test default values for permissions"""
        minimal_data = {
            'user': self.user,
            'organization': self.organization
        }

        org_user = OrganizationUser.objects.create(**minimal_data)

        self.assertFalse(org_user.is_admin)  # Default False
        self.assertTrue(org_user.can_edit)   # Default True
        self.assertTrue(org_user.can_add)    # Default True

    def test_admin_user_creation(self):
        """Test creating an admin user"""
        admin_data = self.valid_org_user_data.copy()
        admin_data['is_admin'] = True

        org_user = OrganizationUser.objects.create(**admin_data)
        self.assertTrue(org_user.is_admin)

    def test_restricted_user_creation(self):
        """Test creating a user with restricted permissions"""
        restricted_data = self.valid_org_user_data.copy()
        restricted_data.update({
            'can_edit': False,
            'can_add': False
        })

        org_user = OrganizationUser.objects.create(**restricted_data)
        self.assertFalse(org_user.can_edit)
        self.assertFalse(org_user.can_add)

    def test_many_to_many_relationship(self):
        """Test the many-to-many relationship through OrganizationUser"""
        # Create organization user relationship
        OrganizationUser.objects.create(**self.valid_org_user_data)

        # Test forward relationship
        user_orgs = self.user.organizations.all()
        self.assertIn(self.organization, user_orgs)

        # Test reverse relationship
        org_users = self.organization.users.all()
        self.assertIn(self.user, org_users)


class ItemImageModelTest(TestCase):
    """Test cases for the ItemImage model"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001'
        )

        self.item = Item.objects.create(
            item_name='Test Item',
            organization=self.organization
        )

        # Create test image content
        self.image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'

        self.valid_image_data = {
            'item': self.item,
            'image': SimpleUploadedFile("test.png", self.image_content, content_type="image/png"),
            'caption': 'Test image caption',
            'is_primary': False,
            'order': 0
        }

    def test_create_item_image(self):
        """Test creating an item image"""
        item_image = ItemImage.objects.create(**self.valid_image_data)

        self.assertEqual(item_image.item, self.item)
        self.assertTrue(item_image.image)
        self.assertEqual(item_image.caption, 'Test image caption')
        self.assertFalse(item_image.is_primary)
        self.assertEqual(item_image.order, 0)

    def test_item_image_str_representation(self):
        """Test string representation"""
        item_image = ItemImage.objects.create(**self.valid_image_data)
        expected_str = f"Image for {self.item.item_name}"
        self.assertEqual(str(item_image), expected_str)

    def test_primary_image_enforcement(self):
        """Test that only one image can be primary per item"""
        # Create first image as primary
        image1_data = self.valid_image_data.copy()
        image1_data['image'] = SimpleUploadedFile("test1.png", self.image_content, content_type="image/png")
        image1_data['is_primary'] = True
        image1 = ItemImage.objects.create(**image1_data)

        self.assertTrue(image1.is_primary)

        # Create second image as primary
        image2_data = self.valid_image_data.copy()
        image2_data['image'] = SimpleUploadedFile("test2.png", self.image_content, content_type="image/png")
        image2_data['is_primary'] = True
        image2_data['order'] = 1
        image2 = ItemImage.objects.create(**image2_data)

        # Refresh first image from database
        image1.refresh_from_db()

        # Only the second image should be primary now
        self.assertFalse(image1.is_primary)
        self.assertTrue(image2.is_primary)

    def test_image_ordering(self):
        """Test image ordering"""
        images = []
        for i in range(3):
            image_data = self.valid_image_data.copy()
            image_data['image'] = SimpleUploadedFile(f"test{i}.png", self.image_content, content_type="image/png")
            image_data['order'] = i
            image_data['caption'] = f'Image {i}'
            images.append(ItemImage.objects.create(**image_data))

        # Get images in order
        ordered_images = list(ItemImage.objects.filter(item=self.item).order_by('order'))

        for i, image in enumerate(ordered_images):
            self.assertEqual(image.order, i)
            self.assertEqual(image.caption, f'Image {i}')

    def test_relationship_with_item(self):
        """Test relationship with Item model"""
        item_image = ItemImage.objects.create(**self.valid_image_data)

        # Test forward relationship
        self.assertEqual(item_image.item, self.item)

        # Test reverse relationship
        item_images = self.item.item_images.all()
        self.assertIn(item_image, item_images)

    def test_default_values(self):
        """Test default values"""
        minimal_data = {
            'item': self.item,
            'image': SimpleUploadedFile("minimal.png", self.image_content, content_type="image/png")
        }

        item_image = ItemImage.objects.create(**minimal_data)

        self.assertEqual(item_image.caption, '')  # Default empty string
        self.assertFalse(item_image.is_primary)   # Default False
        self.assertEqual(item_image.order, 0)     # Default 0

    def test_image_upload_path(self):
        """Test that images are uploaded to correct path"""
        item_image = ItemImage.objects.create(**self.valid_image_data)

        # Check that image path starts with 'item_images/'
        self.assertTrue(item_image.image.name.startswith('item_images/'))


class IntegrationTest(TestCase):
    """Integration tests for model interactions"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.organization = Organization.objects.create(
            name='Test Organization',
            code='TEST001'
        )

        # Create organization user relationship
        self.org_user = OrganizationUser.objects.create(
            user=self.user,
            organization=self.organization,
            is_admin=True
        )

    def test_complete_item_workflow(self):
        """Test complete item creation workflow with all relationships"""
        # Create managed list values
        item_type = ManagedListValue.objects.create(
            organization=self.organization,
            list_name='ItemTypes',
            value='Electronics'
        )

        item_status = ManagedListValue.objects.create(
            organization=self.organization,
            list_name='ItemStatuses',
            value='Available'
        )

        # Create container item
        container = Item.objects.create(
            item_name='Storage Box',
            item_description='A container for storing items',
            organization=self.organization,
            item_type=item_type,
            status=item_status
        )

        # Create item inside container
        item = Item.objects.create(
            item_name='Laptop',
            item_description='Dell Laptop',
            organization=self.organization,
            item_type=item_type,
            status=item_status,
            located_in=container
        )

        # Add image to item
        image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
        item_image = ItemImage.objects.create(
            item=item,
            image=SimpleUploadedFile("laptop.png", image_content, content_type="image/png"),
            caption='Laptop photo',
            is_primary=True
        )

        # Verify all relationships work
        self.assertEqual(item.organization, self.organization)
        self.assertEqual(item.located_in, container)
        self.assertEqual(container.contained_item_count, 1)
        self.assertEqual(item.item_type, item_type)
        self.assertEqual(item.status, item_status)
        self.assertIn(item, item_type.items_of_type.all())
        self.assertIn(item, item_status.items_with_status.all())
        self.assertIn(item_image, item.item_images.all())



        # Verify organization user relationship
        self.assertIn(self.organization, self.user.organizations.all())
        self.assertTrue(self.org_user.is_admin)


class LocationBreadcrumbFilterTest(TestCase):
    """Test cases for the location_breadcrumb template filter"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001'
        )

    def test_location_breadcrumb_no_location(self):
        """Test breadcrumb for item with no location"""
        item = Item.objects.create(
            item_name='Top Level Item',
            organization=self.organization
        )

        result = location_breadcrumb(item)
        self.assertIn('Top Level', result)
        self.assertIn('text-muted', result)

    def test_location_breadcrumb_single_level(self):
        """Test breadcrumb for item with one level of location"""
        container = Item.objects.create(
            item_name='Container',
            organization=self.organization
        )

        item = Item.objects.create(
            item_name='Item',
            organization=self.organization,
            located_in=container
        )

        result = location_breadcrumb(item)
        expected_url = reverse('inventory:item_by_code', args=[container.item_code])

        self.assertIn(container.item_name, result)
        self.assertIn(expected_url, result)
        self.assertIn('<a href=', result)

    def test_location_breadcrumb_two_levels(self):
        """Test breadcrumb for item with two levels of location hierarchy"""
        grandparent = Item.objects.create(
            item_name='Grandparent Container',
            organization=self.organization
        )

        parent = Item.objects.create(
            item_name='Parent Container',
            organization=self.organization,
            located_in=grandparent
        )

        item = Item.objects.create(
            item_name='Item',
            organization=self.organization,
            located_in=parent
        )

        result = location_breadcrumb(item)
        parent_url = reverse('inventory:item_by_code', args=[parent.item_code])
        grandparent_url = reverse('inventory:item_by_code', args=[grandparent.item_code])

        # Should show parent > grandparent format
        self.assertIn(parent.item_name, result)
        self.assertIn(grandparent.item_name, result)
        self.assertIn(parent_url, result)
        self.assertIn(grandparent_url, result)
        self.assertIn('&gt;', result)  # HTML encoded >

    def test_location_breadcrumb_none_item(self):
        """Test breadcrumb with None item"""
        result = location_breadcrumb(None)
        self.assertIn('Top Level', result)
        self.assertIn('text-muted', result)
