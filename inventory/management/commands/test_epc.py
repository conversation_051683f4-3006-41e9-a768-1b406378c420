from django.core.management.base import BaseCommand
from inventory.models import Organization, Item, ManagedListValue


class Command(BaseCommand):
    help = 'Test EPC generation functionality'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing EPC generation functionality...'))
        
        # Create or get test organization
        org, created = Organization.objects.get_or_create(
            code='TESTORG',
            defaults={
                'name': 'Test Organization',
                'rfid_prefix': 42,
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(f'Created test organization: {org.name}')
        else:
            self.stdout.write(f'Using existing organization: {org.name}')
        
        # Create test managed list values
        item_type, _ = ManagedListValue.objects.get_or_create(
            organization=org,
            list_name='ItemTypes',
            value='Test Equipment',
            defaults={'is_active': True}
        )
        
        item_status, _ = ManagedListValue.objects.get_or_create(
            organization=org,
            list_name='ItemStatuses',
            value='Available',
            defaults={'is_active': True}
        )
        
        # Create test item
        item, created = Item.objects.get_or_create(
            item_name='Test EPC Item',
            organization=org,
            defaults={
                'item_description': 'Test item for EPC generation',
                'item_type': item_type,
                'status': item_status
            }
        )
        
        if created:
            self.stdout.write(f'Created test item: {item.item_name}')
        else:
            self.stdout.write(f'Using existing item: {item.item_name}')
        
        # Test EPC generation
        self.stdout.write('\n--- EPC Generation Test Results ---')
        self.stdout.write(f'Item Code: {item.item_code}')
        self.stdout.write(f'Organization Prefix: {org.rfid_prefix}')
        self.stdout.write(f'EPC (hex): {item.epc}')
        self.stdout.write(f'EPC (formatted): {item.epc_formatted}')
        
        # Verify bit structure
        if item.epc:
            epc_int = item.generate_epc()
            item_code_extracted = epc_int & 0xFFFFFF
            padding = (epc_int >> 24) & 0xFFFFFFFFFFFFFFF
            org_prefix = (epc_int >> 84) & 0xFFF

            self.stdout.write('\n--- Bit Structure Verification ---')
            self.stdout.write(f'Extracted org prefix: {org_prefix} (expected: {org.rfid_prefix})')
            self.stdout.write(f'Extracted padding: {padding} (expected: 0)')
            self.stdout.write(f'Extracted item code: {item_code_extracted:06x} (expected: {item.item_code})')

            # Verify correctness
            if (org_prefix == org.rfid_prefix and
                padding == 0 and
                f'{item_code_extracted:06x}' == item.item_code):
                self.stdout.write(self.style.SUCCESS('\n✓ EPC generation is working correctly!'))
            else:
                self.stdout.write(self.style.ERROR('\n✗ EPC generation has issues!'))
        else:
            self.stdout.write(self.style.ERROR('EPC generation failed - no EPC generated'))
        
        # Test multiple items for uniqueness
        self.stdout.write('\n--- Testing EPC Uniqueness ---')
        test_items = []
        for i in range(3):
            test_item = Item.objects.create(
                item_name=f'Test Item {i+1}',
                organization=org,
                item_type=item_type,
                status=item_status
            )
            test_items.append(test_item)
            self.stdout.write(f'Item {i+1} EPC: {test_item.epc}')
        
        # Check uniqueness
        epcs = [item.epc for item in test_items]
        if len(epcs) == len(set(epcs)):
            self.stdout.write(self.style.SUCCESS('✓ All EPCs are unique!'))
        else:
            self.stdout.write(self.style.ERROR('✗ Duplicate EPCs found!'))
        
        # Clean up test items
        for test_item in test_items:
            test_item.delete()
        
        self.stdout.write(self.style.SUCCESS('\nEPC testing completed successfully!'))
