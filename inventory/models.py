import secrets
from django.db import models
from django.core.exceptions import ValidationError
from django.urls import reverse
from django.core.validators import RegexValidator


class ManagedListValue(models.Model):
    """Stores controlled vocabularies for ItemTypes and ItemStatuses"""
    LIST_NAME_CHOICES = [
        ('ItemTypes', 'Item Types'),
        ('ItemStatuses', 'Item Statuses'),
    ]
    
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, related_name='list_values')
    list_name = models.CharField(max_length=50, choices=LIST_NAME_CHOICES)
    value = models.Char<PERSON>ield(max_length=100)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ['organization', 'list_name', 'value']
        indexes = [
            models.Index(fields=['list_name']),
            models.Index(fields=['is_active']),
            models.Index(fields=['organization']),
        ]
    
    def __str__(self):
        return f"{self.value} ({self.list_name})"


class Organization(models.Model):
    """Represents a distinct organization in the system"""
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    is_active = models.BooleanField(default=True)
    
    # RFID prefix for EPC generation (stored as integer)
    rfid_prefix = models.PositiveSmallIntegerField(
        unique=True,
        help_text="Numeric prefix for RFID EPCs (0-4095)"
    )
    
    # Visual identity elements
    logo = models.ImageField(upload_to='org_logos/', null=True, blank=True)
    primary_color = models.CharField(max_length=7, default="#007bff", 
                                    help_text="Primary brand color in hex format (e.g. #007bff)")
    secondary_color = models.CharField(max_length=7, default="#6c757d",
                                      help_text="Secondary brand color in hex format (e.g. #6c757d)")
    
    # Add related field to access users
    users = models.ManyToManyField(
        'auth.User', 
        through='OrganizationUser',
        related_name='organizations'
    )
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        # Auto-assign rfid_prefix if not provided
        if self.rfid_prefix is None and not self.pk:
            # We'll assign the prefix after saving to get the pk
            super().save(*args, **kwargs)
            
            # Now assign the prefix based on pk
            self.assign_rfid_prefix()
            
            # Save again with the prefix
            return super().save(*args, **kwargs)
        else:
            return super().save(*args, **kwargs)
    
    def assign_rfid_prefix(self):
        """Assign a unique RFID prefix by finding the highest prefix in use and incrementing by 1"""
        # Skip if already has a prefix
        if self.rfid_prefix is not None:
            return
        
        # Get the highest existing prefix
        from django.db.models import Max
        highest = Organization.objects.aggregate(Max('rfid_prefix'))['rfid_prefix__max']
        
        # If no prefixes exist yet, start with 0
        if highest is None:
            self.rfid_prefix = 0
        else:
            # Otherwise, increment by 1
            self.rfid_prefix = highest + 1
    
    @property
    def rfid_prefix_hex(self):
        """Return the RFID prefix as a hex string with appropriate length"""
        if self.rfid_prefix is None:
            return None
            
        if self.rfid_prefix < 16:
            # Single hex digit (0-f)
            return format(self.rfid_prefix, 'x')
        elif self.rfid_prefix < 256:
            # Two hex digits (00-ff)
            return format(self.rfid_prefix, '02x')
        else:
            # Three hex digits (000-fff)
            return format(self.rfid_prefix, '03x')


class OrganizationUser(models.Model):
    """Represents a user's membership and permissions in an organization"""
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE)
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE)
    is_admin = models.BooleanField(default=False)
    can_edit = models.BooleanField(default=True)
    can_add = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ('user', 'organization')
        
    def __str__(self):
        return f"{self.user.username} in {self.organization.name}"

    
class Item(models.Model):
    """
    Represents a physical item in the inventory system.
    """
    # Core fields
    _item_code = models.BinaryField(max_length=3, unique=True, editable=False, db_column='item_code')


    @property
    def item_code(self):
        """Return the item code as a hexadecimal string"""
        if self._item_code:
            return self._item_code.hex()
        return None

    @item_code.setter
    def item_code(self, value):
        """Set the item code from a hexadecimal string"""
        if value:
            if isinstance(value, str):
                self._item_code = bytes.fromhex(value)
            else:
                self._item_code = value



    item_name = models.CharField(max_length=255)
    item_description = models.TextField(null=True, blank=True)
    
    # Relationships
    item_type = models.ForeignKey(
        ManagedListValue, 
        on_delete=models.PROTECT,
        related_name='items_of_type',
        limit_choices_to={'list_name': 'ItemTypes', 'is_active': True},
        null=True,
        blank=True
    )
    status = models.ForeignKey(
        ManagedListValue,
        on_delete=models.PROTECT,
        related_name='items_with_status',
        limit_choices_to={'list_name': 'ItemStatuses', 'is_active': True},
        null=True,
        blank=True
    )
    located_in = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='contained_items'
    )
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='items')
    
    # Additional data
    # images = models.JSONField(default=list)  # Keep temporarily for migration
    custom_fields = models.JSONField(default=dict)
    
    # Metadata
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    is_archived = models.BooleanField(default=False)
    
    # Add direct image field to Item model
    image = models.ImageField(upload_to='item_images/', null=True, blank=True)
    image_caption = models.CharField(max_length=255, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['_item_code']),  # Changed from 'item_code'
            models.Index(fields=['located_in']),
            models.Index(fields=['is_archived']),
            models.Index(fields=['item_type']),
            models.Index(fields=['status']),
            models.Index(fields=['date_added']),
            models.Index(fields=['last_updated']),
        ]
        # Add constraints to ensure items belong to valid organizations
        constraints = [
            models.CheckConstraint(
                check=models.Q(organization__isnull=False),
                name='item_must_have_organization'
            )
        ]
    
    def __str__(self):
        return f"{self.item_name} ({self.item_code})"
    
    def validate_location_hierarchy(self, new_location):
        """Prevent circular references in the location hierarchy"""
        if not new_location:
            return True
        
        # Check for direct self-reference
        if self.pk and self.pk == new_location.pk:
            raise ValidationError("An item cannot be located inside itself.")
        
        # Check for circular reference
        current = new_location
        visited = set()
        
        while current and current.located_in:
            if current.pk in visited:
                raise ValidationError("Circular reference detected in location hierarchy.")
            if current.located_in.pk == self.pk:
                raise ValidationError("This would create a circular reference in the location hierarchy.")
            visited.add(current.pk)
            current = current.located_in
        
        return True

    def save(self, *args, **kwargs):
        # Generate item_code if this is a new record
        if not self.pk and not self.item_code:
            self.item_code = self.generate_item_code()
        

        
        # Validate location hierarchy
        self.validate_location_hierarchy(self.located_in)
        
        super().save(*args, **kwargs)
    
    def generate_item_code(self):
        """Generate a unique 3-byte binary value for the item code"""
        max_attempts = 10
        for attempt in range(max_attempts):
            # Generate a random 3-byte value
            code_bytes = secrets.token_bytes(3)
            
            # Check if this code already exists - use raw query to avoid type issues
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute(
                    "SELECT COUNT(*) FROM inventory_item WHERE item_code = %s",
                    [code_bytes]
                )
                if cursor.fetchone()[0] == 0:
                    return code_bytes
        
        # If we reached max attempts, raise an exception
        raise ValidationError("Failed to generate a unique item code after multiple attempts.")
    
    def get_absolute_url(self):
        """Get the URL for the item detail page using item code"""
        return reverse('inventory:item_by_code', args=[self.item_code])
    
    @property
    def contained_item_count(self):
        """Count the number of items located in this item"""
        return self.contained_items.filter(is_archived=False).count()
    
    @property
    def primary_image(self):
        """Return self as a compatible object for templates"""
        if not self.image:
            return None
        return self
    
    @property
    def has_images(self):
        """Check if the item has an image"""
        return bool(self.image)
    
    # Make image URL accessible through the same interface as before
    @property
    def image_url(self):
        if self.image:
            return self.image.url
        return None
    
    def clean(self):
        super().clean()


class ItemImage(models.Model):
    """Stores images associated with inventory items"""
    item = models.ForeignKey('Item', on_delete=models.CASCADE, related_name='item_images')
    image = models.ImageField(upload_to='item_images/')
    caption = models.CharField(max_length=255, blank=True)
    is_primary = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)
    
    class Meta:
        ordering = ['order']
        
    def __str__(self):
        return f"Image for {self.item.item_name}"
    
    def save(self, *args, **kwargs):
        # If this is marked as primary, ensure no other image for this item is primary
        if self.is_primary:
            ItemImage.objects.filter(item=self.item, is_primary=True).exclude(pk=self.pk).update(is_primary=False)
        super().save(*args, **kwargs)
