{% load inventory_filters %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Inventory Management System{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- FontAwesome CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Organization-specific theme -->
    {% if request.session.org_id %}
        {% with org=organizations|filter_by_id:request.session.org_id %}
            <style>
                :root {
                    --primary: {{ org.primary_color }};
                    --secondary: {{ org.secondary_color }};
                    --bs-primary: {{ org.primary_color }};
                    --bs-primary-rgb: {{ org.primary_color|hex_to_rgb }};
                    --bs-secondary: {{ org.secondary_color }};
                    --bs-secondary-rgb: {{ org.secondary_color|hex_to_rgb }};
                }

                .bg-primary {
                    background-color: var(--primary) !important;
                }

                .bg-secondary {
                    background-color: var(--secondary) !important;
                }

                .btn-primary {
                    background-color: var(--primary);
                    border-color: var(--primary);
                }

                .btn-outline-primary {
                    color: var(--primary);
                    border-color: var(--primary);
                }

                .btn-outline-primary:hover {
                    background-color: var(--primary);
                    border-color: var(--primary);
                }

                .navbar-dark {
                    background-color: var(--primary) !important;
                }

                a {
                    color: var(--primary);
                }

                .page-item.active .page-link {
                    background-color: var(--primary);
                    border-color: var(--primary);
                }
            </style>
        {% endwith %}
    {% endif %}

    <!-- Custom CSS -->
    <style>
        .navbar { margin-bottom: 20px; }
        .item-container { border-left: 3px solid var(--primary, #007bff); padding-left: 10px; }
        .item-image { max-width: 150px; max-height: 150px; }
        .code-box {
            padding: 10px;
            border: 1px solid #ddd;
            display: inline-block;
            font-family: monospace;
            font-size: 1.2em;
            margin-bottom: 10px;
            background-color: #f8f9fa;
        }

        /* QR Scanner Modal Styles */
        .qr-scanner-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 1060;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qr-scanner-content {
            background: white;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .qr-scanner-header {
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .qr-scanner-header h5 {
            margin: 0;
            color: #212529;
        }

        .btn-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6c757d;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-close:hover {
            color: #000;
        }

        .qr-scanner-body {
            padding: 1rem;
            text-align: center;
        }

        .qr-video {
            width: 100%;
            max-width: 400px;
            height: auto;
            border-radius: 4px;
            background-color: #000;
        }

        .qr-scanner-status {
            margin-top: 1rem;
            padding: 0.5rem;
            background-color: #f8f9fa;
            border-radius: 4px;
            color: #6c757d;
        }

        .qr-scanner-status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .qr-scanner-status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .qr-scanner-footer {
            padding: 1rem;
            border-top: 1px solid #dee2e6;
            text-align: right;
        }

        /* Mobile QR Scanner Button Styles */
        #header-qr-scanner-btn {
            border-radius: 50%;
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
        }

        /* Ensure proper spacing on mobile */
        @media screen and (max-width: 991.98px) {
            #header-qr-scanner-btn {
                margin-right: 0.5rem !important;
            }
        }

        /* iOS specific adjustments */
        @media screen and (max-width: 768px) {
            .qr-scanner-content {
                width: 95%;
                margin: 1rem;
            }

            .qr-video {
                max-width: 100%;
            }

            /* Ensure QR button is properly sized on mobile */
            #header-qr-scanner-btn {
                width: 38px;
                height: 38px;
                font-size: 1rem;
            }
        }
    </style>
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.9"></script>

    <!-- Extra CSS block for page-specific styles -->
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'inventory:item_list' %}">
                {% if request.session.org_id %}
                    {% with org=organizations|filter_by_id:request.session.org_id %}
                        {% if org.logo %}
                            <img src="{{ org.logo.url }}" alt="{{ org.name }}" height="30" class="me-2">
                        {% endif %}
                        {{ org.name }} Inventory
                    {% endwith %}
                {% else %}
                    Inventory System
                {% endif %}
            </a>
            <!-- QR Scanner Button - Outside collapsible navbar for mobile visibility -->
            {% if user.is_authenticated %}
            <button type="button" id="header-qr-scanner-btn" class="btn btn-outline-light me-2" style="display: none;" title="Scan Item QR Code">
                <i class="fas fa-qrcode"></i>
            </button>
            {% endif %}

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'inventory:item_list' %}">Items</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'inventory:add_item' %}">Add Item</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'inventory:item_lookup' %}">Item Lookup</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'inventory:generate_labels' %}">Generate Labels</a>
                    </li>
                    {% if user.is_staff %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/">Admin</a>
                    </li>
                    {% endif %}
                    <!-- Remove the Manage Users link -->
                </ul>

                <!-- Organization Selector Dropdown -->
                {% if user_org_count > 1 %}
                <div class="dropdown me-3">
                    <button class="btn btn-outline-light dropdown-toggle" type="button" id="orgDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        {% if request.session.org_id %}
                            {% with org=organizations|filter_by_id:request.session.org_id %}
                                {{ org.name }}
                            {% endwith %}
                        {% else %}
                            Select Organization
                        {% endif %}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="orgDropdown">
                        {% for org in organizations %}
                            <li>
                                <a class="dropdown-item {% if org.id|stringformat:'i' == request.session.org_id %}active{% endif %}"
                                   href="{% url 'inventory:switch_organization' org_id=org.id %}?next={{ request.path }}">
                                    {% if org.logo %}
                                        <img src="{{ org.logo.url }}" alt="{{ org.name }}" height="20" class="me-2">
                                    {% endif %}
                                    {{ org.name }}
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
                {% elif user_org_count == 1 and request.session.org_id %}
                <div class="me-3">
                    {% with org=organizations|filter_by_id:request.session.org_id %}
                        <span class="text-light">{{ org.name }}</span>
                    {% endwith %}
                </div>
                {% endif %}

                <div class="navbar-nav">
                    {% if user.is_authenticated %}
                        <span class="nav-link">{{ user.username }}</span>
                        <a class="nav-link" href="{% url 'logout' %}">Logout</a>
                    {% else %}
                        <a class="nav-link" href="{% url 'login' %}">Login</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}{% endblock %}
    </div>

    <!-- Header QR Scanner Modal -->
    {% if user.is_authenticated %}
    <div id="header-qr-scanner-modal" class="qr-scanner-modal" style="display: none;">
        <div class="qr-scanner-content">
            <div class="qr-scanner-header">
                <h5>Scan Item QR Code</h5>
                <button type="button" id="header-close-scanner" class="btn-close">&times;</button>
            </div>
            <div class="qr-scanner-body">
                <video id="header-qr-video" class="qr-video"></video>
                <div id="header-qr-scanner-status" class="qr-scanner-status">
                    Position the QR code within the camera view
                </div>
            </div>
            <div class="qr-scanner-footer">
                <button type="button" id="header-cancel-scanner" class="btn btn-secondary">Cancel</button>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Header QR Scanner JavaScript -->
    {% if user.is_authenticated %}
    <script type="module">
        document.addEventListener('DOMContentLoaded', function() {
            initializeHeaderQRScanner();
        });

        function initializeHeaderQRScanner() {
            // iOS/Safari detection
            const userAgent = navigator.userAgent;
            const isIOS = /iPad|iPhone|iPod/.test(userAgent);
            const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
            const isWebKit = /WebKit/.test(userAgent);

            // Show button on iOS devices or Safari browsers
            const shouldShowButton = isIOS || isSafari || isWebKit;

            // Get all DOM elements
            const qrButton = document.getElementById('header-qr-scanner-btn');
            const qrModal = document.getElementById('header-qr-scanner-modal');
            const qrVideo = document.getElementById('header-qr-video');
            const closeScanner = document.getElementById('header-close-scanner');
            const cancelScanner = document.getElementById('header-cancel-scanner');
            const statusDiv = document.getElementById('header-qr-scanner-status');

            if (qrButton && shouldShowButton) {
                qrButton.style.display = 'inline-block';
            }

            let qrScanner = null;
            let isScanning = false;

            // Open QR scanner
            if (qrButton) {
                qrButton.addEventListener('click', async function() {
                    try {
                        await startQRScanner();
                    } catch (error) {
                        console.error('Failed to start QR scanner:', error);
                        showScannerStatus('Camera access denied or not available. Please check permissions.', 'error');
                    }
                });
            }

            // Close scanner events
            [closeScanner, cancelScanner].forEach(btn => {
                if (btn) {
                    btn.addEventListener('click', stopQRScanner);
                }
            });

            // Close on modal background click
            if (qrModal) {
                qrModal.addEventListener('click', function(e) {
                    if (e.target === qrModal) {
                        stopQRScanner();
                    }
                });
            }

            async function startQRScanner() {
                if (isScanning) return;

                // Check if modal element exists
                if (!qrModal) {
                    console.error('QR Modal element not found');
                    alert('QR Scanner not available');
                    return;
                }

                try {
                    // Check for camera support
                    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                        throw new Error('Camera API not supported in this browser');
                    }

                    // Show modal
                    qrModal.style.display = 'flex';
                    qrModal.style.zIndex = '9999';

                    if (statusDiv) {
                        showScannerStatus('Loading QR scanner library...', '');
                    }

                    // Small delay to ensure modal is visible
                    await new Promise(resolve => setTimeout(resolve, 100));

                    // Import QR Scanner library
                    const QrScanner = (await import('https://cdn.jsdelivr.net/npm/qr-scanner@1.4.2/qr-scanner.min.js')).default;

                    showScannerStatus('Starting camera...', '');

                    // Check video element
                    if (!qrVideo) {
                        throw new Error('Video element not found');
                    }

                    // Initialize scanner
                    qrScanner = new QrScanner(
                        qrVideo,
                        result => handleQRResult(result.data),
                        {
                            returnDetailedScanResult: true,
                            preferredCamera: 'environment',
                            highlightScanRegion: true,
                            highlightCodeOutline: true,
                        }
                    );

                    await qrScanner.start();
                    isScanning = true;
                    showScannerStatus('Position the QR code within the camera view', '');

                } catch (error) {
                    console.error('QR Scanner initialization error:', error);
                    let errorMessage = 'Failed to start camera. ';

                    if (error.name === 'NotAllowedError') {
                        errorMessage += 'Camera permission denied. Please allow camera access and try again.';
                    } else if (error.name === 'NotFoundError') {
                        errorMessage += 'No camera found on this device.';
                    } else if (error.name === 'NotSupportedError') {
                        errorMessage += 'Camera not supported in this browser.';
                    } else {
                        errorMessage += error.message || 'Unknown error occurred.';
                    }

                    // Show error in modal if visible, otherwise alert
                    if (qrModal && qrModal.style.display === 'flex') {
                        showScannerStatus(errorMessage, 'error');
                        setTimeout(() => {
                            stopQRScanner();
                        }, 5000);
                    } else {
                        alert('QR Scanner Error: ' + errorMessage);
                    }
                }
            }

            function stopQRScanner() {
                if (qrScanner) {
                    qrScanner.stop();
                    qrScanner.destroy();
                    qrScanner = null;
                }

                isScanning = false;
                qrModal.style.display = 'none';
                showScannerStatus('Position the QR code within the camera view', '');
            }

            function handleQRResult(data) {
                try {
                    let itemCode = null;
                    let format = 'unknown';

                    // First, try to parse as URL format: http://{domain}/{item_code}
                    try {
                        const url = new URL(data);
                        const pathParts = url.pathname.split('/').filter(part => part.length > 0);
                        if (pathParts.length > 0) {
                            itemCode = pathParts[pathParts.length - 1];
                            format = 'url';
                        }
                    } catch (urlError) {
                        // Not a valid URL, try as raw item code format
                        const trimmedData = data.trim();
                        if (trimmedData) {
                            itemCode = trimmedData;
                            format = 'raw';
                        }
                    }

                    // Validate that we extracted an item code
                    if (!itemCode) {
                        showScannerStatus('Invalid QR code: No item code found.', 'error');
                        return;
                    }

                    // Validate item code format (hexadecimal)
                    const hexPattern = /^[0-9A-Fa-f]+$/;
                    if (!hexPattern.test(itemCode)) {
                        showScannerStatus('Invalid QR code: Item code must be hexadecimal (0-9, A-F).', 'error');
                        return;
                    }

                    // Check length constraints (4-8 hex chars, even length)
                    if (itemCode.length < 4 || itemCode.length > 8 || itemCode.length % 2 !== 0) {
                        showScannerStatus('Invalid QR code: Item code must be 4-8 hexadecimal characters (even length).', 'error');
                        return;
                    }

                    // Success - show format-specific message
                    if (format === 'url') {
                        showScannerStatus(`URL QR code found! Navigating to item ${itemCode}...`, 'success');
                    } else {
                        showScannerStatus(`Raw QR code found! Navigating to item ${itemCode}...`, 'success');
                    }

                    // Navigate to item detail page
                    navigateToItem(itemCode.toUpperCase());

                    // Close scanner after successful scan
                    setTimeout(() => {
                        stopQRScanner();
                    }, 1000);

                } catch (error) {
                    console.error('Error processing QR code:', error);
                    showScannerStatus('Invalid QR code format. Please scan a valid item code or URL.', 'error');
                }
            }

            function navigateToItem(itemCode) {
                // Navigate to the item detail page using the item_by_code URL pattern
                const itemUrl = `/${itemCode}`;
                window.location.href = itemUrl;
            }

            function showScannerStatus(message, type) {
                if (statusDiv) {
                    statusDiv.innerHTML = `<p>${message}</p>`;
                    statusDiv.className = 'qr-scanner-status';
                    if (type) {
                        statusDiv.classList.add(type);
                    }
                }
            }
        }
    </script>
    {% endif %}

    {% block extra_js %}{% endblock %}
</body>
</html>

